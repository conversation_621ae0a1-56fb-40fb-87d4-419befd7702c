import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { path: '/dashboard', icon: 'fas fa-chart-line', label: 'Dashboard' },
    { path: '/chatbot', icon: 'fas fa-comments', label: 'Chatbot' },
    { path: '/settings', icon: 'fas fa-cog', label: 'Settings' },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Menu Toggle Button */}
      <button
        onClick={toggleMobileMenu}
        className="md:hidden fixed bottom-5 right-5 z-50 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
      >
        <i className="fas fa-bars text-xl"></i>
      </button>

      {/* Sidebar */}
      <div className={`
        bg-gray-50 dark:bg-gray-900 w-60 p-5 border-r border-gray-200 dark:border-gray-700 
        flex flex-col transition-all duration-300 shadow-sm
        md:relative md:translate-x-0
        ${isMobileMenuOpen ? 'fixed inset-y-0 left-0 z-40 translate-x-0' : 'fixed inset-y-0 left-0 z-40 -translate-x-full md:translate-x-0'}
      `}>
        
        {/* Navigation Header */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-4 mb-5">
          <i className="fas fa-bars mr-3"></i>
          Navigation Menu
        </h3>

        {/* Navigation Items */}
        <nav className="flex-1">
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              onClick={closeMobileMenu}
              className={({ isActive }) => `
                flex items-center px-4 py-3 mb-2 rounded-lg text-sm font-medium transition-all duration-200
                ${isActive 
                  ? 'bg-blue-600 text-white shadow-md' 
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 hover:translate-x-1'
                }
              `}
            >
              <i className={`${item.icon} mr-3 w-4`}></i>
              {item.label}
            </NavLink>
          ))}
        </nav>
      </div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={closeMobileMenu}
        ></div>
      )}
    </>
  );
};

export default Sidebar;
