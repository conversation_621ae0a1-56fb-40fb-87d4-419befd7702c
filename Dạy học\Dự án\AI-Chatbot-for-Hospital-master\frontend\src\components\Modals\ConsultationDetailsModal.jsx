import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../UI/LoadingSpinner';

const ConsultationDetailsModal = ({ isOpen, onClose, consultationId, onPatientClick, onDoctorClick }) => {
  const [consultationData, setConsultationData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [isAnimating, setIsAnimating] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (isOpen && consultationId) {
      setIsAnimating(true);
      setShowContent(false);
      fetchConsultationDetails();
    } else if (!isOpen) {
      setIsAnimating(false);
      setShowContent(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, consultationId]);

  const fetchConsultationDetails = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch(`/api/v0/consultations/${consultationId}/details`);
      const data = await response.json();
      
      if (response.ok) {
        setConsultationData(data.data);
        // Smooth transition to show content
        setTimeout(() => setShowContent(true), 100);
      } else {
        setError(data.error || 'Failed to fetch consultation details');
      }
    } catch (error) {
      console.error('Error fetching consultation details:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return 'N/A';
    try {
      return new Date(dateTimeString).toLocaleString();
    } catch {
      return dateTimeString;
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black transition-opacity duration-300 flex items-center justify-center z-50 p-4 ${
      isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
    }`}>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full h-[75vh] flex flex-col overflow-hidden transform transition-all duration-300 ${
        isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            Consultation Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex min-h-0">
          {loading ? (
            <div className="flex items-center justify-center flex-1">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center flex-1 p-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-md">
                <p className="text-red-700 dark:text-red-400">{error}</p>
                <button
                  onClick={fetchConsultationDetails}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 underline"
                >
                  Try again
                </button>
              </div>
            </div>
          ) : consultationData ? (
            <div className="flex flex-1 min-h-0">
              {/* Sidebar Navigation */}
              <div className="w-48 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-3 flex-shrink-0">
                <nav className="space-y-2">
                  {[
                    { id: 'overview', label: 'Overview', icon: 'fas fa-stethoscope' },
                    { id: 'symptoms', label: 'Symptoms', icon: 'fas fa-thermometer-half' },
                    { id: 'observations', label: 'Observations', icon: 'fas fa-eye' },
                    { id: 'biometrics', label: 'Vital Signs', icon: 'fas fa-heartbeat' },
                    { id: 'prescriptions', label: 'Prescriptions', icon: 'fas fa-pills' },
                    { id: 'procedures', label: 'Procedures', icon: 'fas fa-procedures' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left px-3 py-2.5 rounded text-sm font-medium transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 shadow-sm'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200'
                      }`}
                    >
                      <i className={`${tab.icon} mr-3 text-base`}></i>
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Main Content */}
              <div className={`flex-1 overflow-y-auto p-4 min-h-0 transition-opacity duration-300 ${
                showContent && !loading ? 'opacity-100' : 'opacity-0'
              }`}>
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Consultation Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Consultation ID</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{consultationData.consultation_info.consultation_id || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Date & Time</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{formatDateTime(consultationData.consultation_info.consultation_date)}</p>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Diagnosis</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{consultationData.consultation_info.diagnosis || 'N/A'}</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Patient & Doctor Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Patient</label>
                          <button
                            onClick={() => onPatientClick && onPatientClick(consultationData.consultation_info.patient_id)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline text-base font-medium"
                          >
                            {consultationData.consultation_info.patient_name || 'N/A'}
                          </button>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Doctor</label>
                          <div>
                            <button
                              onClick={() => onDoctorClick && onDoctorClick(consultationData.consultation_info.doctor_id)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline text-base font-medium"
                            >
                              {consultationData.consultation_info.doctor_name || 'N/A'}
                            </button>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {consultationData.consultation_info.doctor_specialty || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'symptoms' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Symptoms</h3>
                    {consultationData.symptoms.length > 0 ? (
                      <div className="space-y-4">
                        {consultationData.symptoms.map((symptom, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">
                              {symptom.symptom_name}
                            </h4>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No symptoms recorded for this consultation.</p>
                    )}
                  </div>
                )}

                {activeTab === 'observations' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Clinical Observations</h3>
                    {consultationData.observations.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                          <thead className="bg-gray-50 dark:bg-gray-900">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Observation</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Date</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {consultationData.observations.map((observation, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {observation.observation_text || 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {formatDateTime(observation.observation_date)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No clinical observations recorded.</p>
                    )}
                  </div>
                )}

                {activeTab === 'biometrics' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Vital Signs (Around Consultation)</h3>
                    {consultationData.biometrics.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                          <thead className="bg-gray-50 dark:bg-gray-900">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Date</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Temperature</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Blood Pressure</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Pulse</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">SpO2</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Resp. Rate</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {consultationData.biometrics.map((reading, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {formatDateTime(reading.date)}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.temperature ? `${reading.temperature}°C` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.systolic_bp && reading.diastolic_bp
                                    ? `${reading.systolic_bp}/${reading.diastolic_bp}`
                                    : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.pulse ? `${reading.pulse} bpm` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.spo2 ? `${reading.spo2}%` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.respiratory_rate ? `${reading.respiratory_rate}/min` : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No vital signs recorded around this consultation.</p>
                    )}
                  </div>
                )}

                {activeTab === 'prescriptions' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Prescriptions</h3>
                    {consultationData.prescriptions.length > 0 ? (
                      <div className="space-y-4">
                        {consultationData.prescriptions.map((prescription, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                {prescription.medication_name}
                              </h4>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDate(prescription.date)}
                              </span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Form:</span>
                                <span className="ml-2 text-gray-900 dark:text-gray-100">{prescription.form || 'N/A'}</span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Dosage:</span>
                                <span className="ml-2 text-gray-900 dark:text-gray-100">{prescription.dosage || 'N/A'}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No prescriptions given during this consultation.</p>
                    )}
                  </div>
                )}

                {activeTab === 'procedures' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Procedures</h3>
                    {consultationData.procedures.length > 0 ? (
                      <div className="space-y-4">
                        {consultationData.procedures.map((procedure, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                {procedure.procedure_name || procedure.procedure_type || 'Procedure'}
                              </h4>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDateTime(procedure.date)}
                              </span>
                            </div>
                            {procedure.procedure_type && procedure.procedure_type !== procedure.procedure_name && (
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                Type: {procedure.procedure_type}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No procedures performed during this consultation.</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ConsultationDetailsModal;
