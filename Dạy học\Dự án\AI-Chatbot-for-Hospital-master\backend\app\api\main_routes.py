import logging
from fastapi import <PERSON>Rout<PERSON>, HTTPException, Request
from fastapi.responses import FileResponse

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/health")
async def health_check(request: Request):
    """Health check endpoint for monitoring"""
    vn = request.app.state.vn

    try:
        # Check database connection
        vn.run_sql("SELECT 1")
        return {"status": "healthy", "message": "Service is operational"}
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {"status": "unhealthy", "message": str(e)}

@router.get("/static/{filename:path}")
async def serve_static(filename: str):
    """Serve static files"""
    try:
        return FileResponse(f"static/{filename}")
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
