/* Chat History Sidebar Styles */
.chat-history-sidebar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.dark .chat-history-sidebar {
  scrollbar-color: #4a5568 #2d3748;
}

.chat-history-sidebar::-webkit-scrollbar {
  width: 6px;
}

.chat-history-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.dark .chat-history-sidebar::-webkit-scrollbar-track {
  background: #2d3748;
}

.chat-history-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.dark .chat-history-sidebar::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.chat-history-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.dark .chat-history-sidebar::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Session Item Animations */
.session-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
}

.session-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .session-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.session-item.active {
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  transform: translateX(2px);
}

.dark .session-item.active {
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

/* Smooth session switching */
.session-item:active {
  transform: translateX(1px) scale(0.98);
  transition: all 0.1s ease;
}

/* Action buttons fade in/out */
.session-actions {
  transition: opacity 0.2s ease-in-out;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .chat-history-sidebar {
    width: 100vw;
    max-width: 320px;
  }
}

/* Loading states */
.session-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .session-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth transitions for sidebar */
.sidebar-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
.session-item:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.session-input:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* Custom checkbox styles for bulk actions */
.session-checkbox {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.2s;
}

.session-checkbox:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.session-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 1px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.dark .session-checkbox {
  border-color: #6b7280;
  background: #374151;
}

.dark .session-checkbox:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

/* Drag and drop styles */
.session-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.session-drop-zone {
  border: 2px dashed #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Search input styles */
.session-search {
  transition: all 0.2s ease-in-out;
}

.session-search:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Empty state styles */
.empty-state {
  opacity: 0.6;
  text-align: center;
  padding: 2rem;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

/* Compact mode styles */
.compact-mode .session-item {
  padding: 8px 12px;
}

.compact-mode .session-title {
  font-size: 13px;
}

.compact-mode .session-preview {
  display: none;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .session-item {
    border: 1px solid;
  }
  
  .session-item.active {
    border-width: 2px;
  }
}
