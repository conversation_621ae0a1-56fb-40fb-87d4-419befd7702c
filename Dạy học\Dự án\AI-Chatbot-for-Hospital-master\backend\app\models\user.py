import hashlib
from pydantic import BaseModel
from typing import Optional

class User:
    """User model for FastAPI authentication"""

    def __init__(self, id: str, username: str, password_hash: str, role: str):
        self.id = id
        self.username = username
        self.password_hash = password_hash  # Store hashed passwords only
        self.role = role

    def get_id(self):
        return str(self.id)
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def check_password(self, password: str) -> bool:
        """Check if provided password matches the stored hash"""
        return self.password_hash == self.hash_password(password)

# Mock user database - in production, use a real database
USERS_DB = {
    '1': User('1', 'admin', User.hash_password('admin123'), 'admin'),
    '2': User('2', 'doctor', User.hash_password('doctor123'), 'doctor'),
    '3': User('3', 'nurse', User.hash_password('nurse123'), 'nurse')
}

def get_user_by_id(user_id: str) -> User:
    """Get user by ID"""
    return USERS_DB.get(user_id)

def get_user_by_username(username: str) -> User:
    """Get user by username"""
    return next((user for user in USERS_DB.values() if user.username == username), None)
