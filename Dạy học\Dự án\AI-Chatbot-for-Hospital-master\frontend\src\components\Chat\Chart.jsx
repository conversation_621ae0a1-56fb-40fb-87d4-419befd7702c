import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

// Theme-aware color palettes
const getColorPalette = (isDark) => ({
  primary: isDark ? '#60A5FA' : '#3B82F6',
  secondary: isDark ? '#34D399' : '#10B981',
  accent: isDark ? '#FBBF24' : '#F59E0B',
  danger: isDark ? '#F87171' : '#EF4444',
  purple: isDark ? '#A78BFA' : '#8B5CF6',
  indigo: isDark ? '#818CF8' : '#6366F1',
  pink: isDark ? '#F472B6' : '#EC4899',
  teal: isDark ? '#2DD4BF' : '#14B8A6',
  text: isDark ? '#E5E7EB' : '#374151',
  textSecondary: isDark ? '#9CA3AF' : '#6B7280',
  background: isDark ? '#1F2937' : '#FFFFFF',
  surface: isDark ? '#374151' : '#F9FAFB'
});

const Chart = ({ data }) => {
  const chartRef = useRef(null);
  const { isDark } = useTheme();

  useEffect(() => {
    if (!data || !chartRef.current || !window.Plotly) {
      return;
    }

    try {
      // Parse the chart data if it's a string
      const chartData = typeof data === 'string' ? JSON.parse(data) : data;
      const colors = getColorPalette(isDark);

      // Apply theme-appropriate template
      const template = isDark ? 'plotly_dark' : 'plotly_white';

      // Enhanced layout for better dark mode support
      const layout = {
        ...chartData.layout,
        template: template,
        autosize: true,
        margin: { l: 60, r: 60, t: 60, b: 60 },
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: {
          family: 'Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
          size: 12,
          color: colors.text
        },
        // Enhanced grid and axis styling
        xaxis: {
          ...chartData.layout?.xaxis,
          gridcolor: isDark ? '#374151' : '#E5E7EB',
          linecolor: isDark ? '#4B5563' : '#D1D5DB',
          tickcolor: isDark ? '#4B5563' : '#D1D5DB',
          titlefont: { color: colors.text },
          tickfont: { color: colors.textSecondary }
        },
        yaxis: {
          ...chartData.layout?.yaxis,
          gridcolor: isDark ? '#374151' : '#E5E7EB',
          linecolor: isDark ? '#4B5563' : '#D1D5DB',
          tickcolor: isDark ? '#4B5563' : '#D1D5DB',
          titlefont: { color: colors.text },
          tickfont: { color: colors.textSecondary }
        },
        // Enhanced legend styling
        legend: {
          ...chartData.layout?.legend,
          font: { color: colors.text },
          bgcolor: 'rgba(0,0,0,0)',
          bordercolor: isDark ? '#4B5563' : '#D1D5DB'
        },
        // Enhanced title styling
        title: {
          ...chartData.layout?.title,
          font: {
            color: colors.text,
            size: 16,
            family: 'Segoe UI, Roboto, sans-serif'
          }
        }
      };

      // Enhanced configuration with modebar disabled to prevent chart obstruction
      const config = {
        responsive: true,
        displayModeBar: false,
        displaylogo: false,
        toImageButtonOptions: {
          format: 'png',
          filename: 'hospital_chart',
          height: 600,
          width: 800,
          scale: 2
        }
      };

      // Apply theme-aware colors to chart data
      const themedData = chartData.data.map(trace => {
        const themedTrace = { ...trace };

        // Apply theme-aware colors based on trace type
        if (trace.type === 'bar') {
          if (!trace.marker?.color || typeof trace.marker.color === 'string') {
            themedTrace.marker = {
              ...trace.marker,
              color: colors.primary,
              line: {
                color: isDark ? '#1F2937' : '#FFFFFF',
                width: 1
              }
            };
          }
        } else if (trace.type === 'scatter') {
          themedTrace.line = {
            ...trace.line,
            color: trace.line?.color || colors.primary
          };
          themedTrace.marker = {
            ...trace.marker,
            color: trace.marker?.color || colors.primary,
            line: {
              color: isDark ? '#1F2937' : '#FFFFFF',
              width: 1
            }
          };
        } else if (trace.type === 'pie') {
          if (!trace.marker?.colors) {
            themedTrace.marker = {
              ...trace.marker,
              colors: [
                colors.primary,
                colors.secondary,
                colors.accent,
                colors.purple,
                colors.indigo,
                colors.pink,
                colors.teal,
                colors.danger
              ],
              line: {
                color: isDark ? '#1F2937' : '#FFFFFF',
                width: 2
              }
            };
          }
        }

        return themedTrace;
      });

      // Create the plot with themed data
      window.Plotly.newPlot(
        chartRef.current,
        themedData,
        layout,
        config
      );

      // Store resize function for responsive behavior
      const resizeChart = () => {
        if (chartRef.current && window.Plotly) {
          window.Plotly.Plots.resize(chartRef.current);
        }
      };

      // Add resize listener
      window.addEventListener('resize', resizeChart);
      
      // Store resize function on the element for external access
      chartRef.current._resizeFunction = resizeChart;

      // Cleanup function
      return () => {
        window.removeEventListener('resize', resizeChart);
        if (chartRef.current && window.Plotly) {
          window.Plotly.purge(chartRef.current);
        }
      };

    } catch (error) {
      console.error('Error rendering chart:', error);
      // Show error message in the chart container with theme awareness
      if (chartRef.current) {
        const colors = getColorPalette(isDark);
        chartRef.current.innerHTML = `
          <div class="flex items-center justify-center h-64" style="color: ${colors.danger}">
            <div class="text-center">
              <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
              <p>Error rendering chart</p>
              <p class="text-sm mt-2" style="color: ${colors.textSecondary}">Please try refreshing the page</p>
            </div>
          </div>
        `;
      }
    }
  }, [data, isDark]);

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <i className="fas fa-chart-bar text-2xl mb-2"></i>
          <p>No chart data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div
        ref={chartRef}
        className="interactive-chart w-full h-96 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm dark:shadow-gray-900/20"
        style={{
          minHeight: '400px',
          transition: 'all 0.3s ease'
        }}
      />

      {/* Enhanced download button with theme awareness */}
      <div className="mt-3 flex justify-between items-center">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <i className="fas fa-info-circle mr-1"></i>
          Interactive chart - hover for details
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => {
              if (chartRef.current && window.Plotly) {
                window.Plotly.downloadImage(chartRef.current, {
                  format: 'png',
                  filename: 'hospital_chart',
                  height: 600,
                  width: 800,
                  scale: 2
                });
              }
            }}
            className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm rounded-md transition-colors shadow-sm hover:shadow-md"
            title="Download as PNG"
          >
            <i className="fas fa-download mr-1"></i>
            Download
          </button>
          <button
            onClick={() => {
              if (chartRef.current && window.Plotly) {
                window.Plotly.Plots.resize(chartRef.current);
              }
            }}
            className="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white text-sm rounded-md transition-colors shadow-sm hover:shadow-md"
            title="Resize chart"
          >
            <i className="fas fa-expand-arrows-alt"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Chart;
