/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Light theme colors
        'bg-primary': '#f8f9fa',
        'bg-secondary': '#ffffff',
        'bg-nav': '#eef2f5',
        'text-primary': '#212529',
        'text-secondary': '#495057',
        'text-muted': '#6c757d',
        'accent': '#007bff',
        'accent-hover': '#0056b3',
        'border': '#dee2e6',
        'soft-border': '#e9ecef',
        'user-bubble': '#007bff',
        'bot-bubble': '#e9ecef',
        
        // Dark theme colors (will be applied via CSS variables)
        'dark-bg-primary': '#121212',
        'dark-bg-secondary': '#1e1e1e',
        'dark-bg-nav': '#242424',
        'dark-text-primary': '#e0e0e0',
        'dark-text-secondary': '#b0b0b0',
        'dark-text-muted': '#888888',
        'dark-accent': '#0d6efd',
        'dark-accent-hover': '#3b82f6',
        'dark-border': '#343a40',
        'dark-soft-border': '#2c2f33',
        'dark-user-bubble': '#0d6efd',
        'dark-bot-bubble': '#343a40',
      },
      fontFamily: {
        'sans': ['Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', 'sans-serif'],
      },
      boxShadow: {
        'header': '0 1px 3px rgba(0,0,0,0.03)',
        'button': '0 2px 4px rgba(0, 0, 0, 0.05)',
        'card': '0 2px 8px rgba(0,0,0,0.05)',
        'widget': '0 4px 16px rgba(0,0,0,0.1)',
      },
      animation: {
        'shimmer': 'shimmer 1.8s linear infinite',
        'fade-in': 'fadeIn 0.4s ease-out',
        'slide-up': 'slideUp 0.4s ease-out',
      },
      keyframes: {
        shimmer: {
          'to': { 'background-position': '-200% center' }
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(15px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        }
      },
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
      }
    },
  },
  plugins: [],
}
