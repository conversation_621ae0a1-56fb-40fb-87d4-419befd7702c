import time
import uuid
from collections import OrderedDict
from typing import Any, List, Dict, Optional

class MemoryCache:
    """Optimized MemoryCache with expiration and size limit"""
    
    def __init__(self, max_size: int = 100, expiration_seconds: int = 3600):
        self.cache = OrderedDict()  # Use OrderedDict for LRU functionality
        self.max_size = max_size
        self.expiration_seconds = expiration_seconds
        self.timestamps = {}  # Track when items were added

    def generate_id(self, question: str = None) -> str:
        return str(uuid.uuid4())

    def set(self, id: str, field: str, value: Any) -> None:
        # Create entry if it doesn't exist
        if id not in self.cache:
            self.cache[id] = {}
            self.timestamps[id] = time.time()
        
        # Update existing entry and timestamp
        self.cache[id][field] = value
        self.timestamps[id] = time.time()
        
        # If we exceeded the max size, remove the oldest item (LRU)
        if len(self.cache) > self.max_size:
            oldest_id = next(iter(self.cache))
            del self.cache[oldest_id]
            del self.timestamps[oldest_id]

    def get(self, id: str, field: str) -> Any:
        # Check if the entry exists and hasn't expired
        if id in self.cache and id in self.timestamps:
            if time.time() - self.timestamps[id] < self.expiration_seconds:
                # Update access time on successful read
                self.timestamps[id] = time.time()
                return self.cache.get(id, {}).get(field)
            else:
                # Remove expired entry
                del self.cache[id]
                del self.timestamps[id]
        return None

    def get_all(self, field_list: List[str]) -> List[Dict[str, Any]]:
        results = []
        current_time = time.time()
        expired_ids = []
        
        for id_key, data in self.cache.items():
            # Check if the item has expired
            if current_time - self.timestamps.get(id_key, 0) >= self.expiration_seconds:
                expired_ids.append(id_key)
                continue
                
            if all(field in data for field in field_list):
                item = {'id': id_key}
                for field in field_list:
                    item[field] = data[field]
                results.append(item)
                
        # Clean up expired entries
        for id_key in expired_ids:
            if id_key in self.cache:
                del self.cache[id_key]
            if id_key in self.timestamps:
                del self.timestamps[id_key]
                
        return results

    def get_item(self, id: str) -> Optional[Dict[str, Any]]:
        if id in self.cache and id in self.timestamps:
            if time.time() - self.timestamps[id] < self.expiration_seconds:
                # Update access time
                self.timestamps[id] = time.time()
                return self.cache.get(id)
            else:
                # Remove expired entry
                del self.cache[id]
                del self.timestamps[id]
        return None
