================================================================================
                    BÁO CÁO HỆ THỐNG LOGIN/LOGOUT
                AI CHATBOT FOR HOSPITAL PROJECT
================================================================================

1. TỔNG QUAN HỆ THỐNG
================================================================================

Hệ thống authentication sử dụng:
- Backend: FastAPI + JWT tokens
- Frontend: React + Context API  
- Database: Mock database (3 user roles)
- Security: Password hashing + Route protection

Mục tiêu: <PERSON><PERSON><PERSON> thực người dùng an toàn và phân quyền theo vai trò.

2. CẤU TRÚC FILE VÀ VAI TRÒ
================================================================================

📁 BACKEND FILES:
backend/app/auth/routes.py       → Core authentication logic, JWT handling
backend/app/models/user.py       → User model, password hashing
backend/app/models/__init__.py   → Mock database, user lookup functions
backend/main.py                  → FastAPI setup, CORS, router registration

📁 FRONTEND FILES:
frontend/src/contexts/AuthContext.jsx        → Authentication state management
frontend/src/pages/Login.jsx                 → Login form UI
frontend/src/components/Auth/ProtectedRoute.jsx → Route protection
frontend/src/components/Layout/Header.jsx    → Logout button
frontend/src/App.jsx                         → Route configuration

3. BACKEND - FASTAPI AUTHENTICATION
================================================================================

🔑 A) USER MODEL VÀ PASSWORD HASHING:

```python
# backend/app/models/user.py
class User:
    def __init__(self, id: str, username: str, password_hash: str, role: str):
        self.id = id
        self.username = username
        self.password_hash = password_hash  # Mật khẩu đã được hash
        self.role = role

    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password bằng SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def check_password(self, password: str) -> bool:
        """Kiểm tra password có đúng không"""
        return self.password_hash == self.hash_password(password)
```

Giải thích: User model lưu trữ thông tin người dùng với password đã được hash.
Phương thức check_password() so sánh password nhập vào với hash đã lưu.

🔑 B) MOCK DATABASE:

```python
# backend/app/models/__init__.py
USERS_DB = {
    '1': User('1', 'admin', User.hash_password('admin123'), 'admin'),
    '2': User('2', 'doctor', User.hash_password('doctor123'), 'doctor'),
    '3': User('3', 'nurse', User.hash_password('nurse123'), 'nurse')
}

def get_user_by_username(username: str) -> User:
    """Tìm user theo username"""
    for user in USERS_DB.values():
        if user.username == username:
            return user
    return None
```

Giải thích: Database giả lập với 3 user có role khác nhau.
Function get_user_by_username() tìm user trong database.

🔑 C) JWT CONFIGURATION VÀ LOGIN ENDPOINT:

```python
# backend/app/auth/routes.py
SECRET_KEY = "your-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

@router.post("/login")
async def login(login_data: LoginRequest):
    # 1. Tìm user trong database
    user = get_user_by_username(login_data.username)
    
    # 2. Kiểm tra user tồn tại và password đúng
    if not user or not user.check_password(login_data.password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # 3. Tạo JWT token
    access_token = create_access_token(
        data={"sub": user.username}, 
        expires_delta=timedelta(minutes=30)
    )
    
    # 4. Trả về token và thông tin user
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {"username": user.username, "role": user.role}
    }
```

Giải thích: 
- Nhận username/password từ frontend
- Tìm user và verify password
- Tạo JWT token có thời hạn 30 phút
- Trả về token để frontend lưu trữ

🔑 D) LOGOUT ENDPOINT:

```python
@router.post("/logout")
async def logout():
    """Logout đơn giản - frontend sẽ xóa token"""
    return {"message": "Logout successful"}
```

Giải thích: Logout chỉ trả về success message. 
Frontend sẽ tự xóa token khỏi localStorage.

4. FRONTEND - REACT AUTHENTICATION
================================================================================

🎨 A) AUTHENTICATION CONTEXT:

```javascript
// frontend/src/contexts/AuthContext.jsx
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Kiểm tra localStorage khi app khởi động
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password })
    });

    if (response.ok) {
      const data = await response.json();
      const userData = {
        username: username,
        role: data.user.role
      };
      
      setUser(userData);                              // Cập nhật state
      localStorage.setItem('user', JSON.stringify(userData)); // Lưu vào localStorage
      return { success: true };
    } else {
      return { success: false, error: 'Login failed' };
    }
  };

  const logout = async () => {
    await fetch('/auth/logout', { method: 'POST' });
    setUser(null);                    // Xóa user state
    localStorage.removeItem('user');  // Xóa localStorage
  };

  return (
    <AuthContext.Provider value={{
      user, login, logout, loading, isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
};
```

Giải thích:
- AuthContext quản lý trạng thái đăng nhập toàn app
- localStorage giúp duy trì session khi refresh page
- login() gọi API và lưu user data
- logout() xóa tất cả thông tin user

🎨 B) LOGIN PAGE:

```javascript
// frontend/src/pages/Login.jsx
const Login = () => {
  const [formData, setFormData] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const { login, isAuthenticated } = useAuth();

  // Redirect nếu đã đăng nhập
  if (isAuthenticated) {
    return <Navigate to="/chatbot" replace />;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    const result = await login(formData.username, formData.password);
    
    if (!result.success) {
      setError(result.error);  // Hiển thị lỗi nếu login fail
    }
    // Nếu success, AuthContext sẽ tự động redirect
  };

  return (
    <form onSubmit={handleSubmit}>
      <input 
        name="username" 
        value={formData.username}
        onChange={(e) => setFormData({...formData, username: e.target.value})}
      />
      <input 
        name="password" 
        type="password"
        value={formData.password}
        onChange={(e) => setFormData({...formData, password: e.target.value})}
      />
      <button type="submit">Login</button>
      {error && <div className="error">{error}</div>}
    </form>
  );
};
```

Giải thích:
- Form đơn giản với username/password
- Kiểm tra isAuthenticated để redirect
- Hiển thị error message nếu login thất bại

🎨 C) PROTECTED ROUTE:

```javascript
// frontend/src/components/Auth/ProtectedRoute.jsx
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) return <LoadingSpinner />;
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};
```

Giải thích:
- Wrapper component bảo vệ các route cần authentication
- Redirect về /login nếu chưa đăng nhập
- Hiển thị loading khi đang kiểm tra auth state

🎨 D) LOGOUT BUTTON:

```javascript
// frontend/src/components/Layout/Header.jsx
const Header = () => {
  const { user, logout } = useAuth();

  return (
    <div className="header">
      <span>Welcome, {user?.username}</span>
      <button onClick={logout}>
        <i className="fas fa-sign-out-alt"></i> Logout
      </button>
    </div>
  );
};
```

Giải thích:
- Hiển thị tên user đang đăng nhập
- Button logout gọi logout() từ AuthContext

5. LUỒNG HOẠT ĐỘNG LOGIN/LOGOUT
================================================================================

🔄 LUỒNG LOGIN:

1. User nhập username/password vào Login form
2. Form gọi login() từ AuthContext
3. AuthContext gửi POST /auth/login đến backend
4. Backend tìm user trong database và verify password
5. Backend tạo JWT token và trả về cùng user info
6. Frontend lưu user data vào localStorage và state
7. isAuthenticated = true → redirect đến /chatbot

🚪 LUỒNG LOGOUT:

1. User click logout button trong Header
2. Header gọi logout() từ AuthContext  
3. AuthContext gửi POST /auth/logout (optional)
4. AuthContext xóa user khỏi state và localStorage
5. isAuthenticated = false → redirect đến /login

🛡️ LUỒNG ROUTE PROTECTION:

1. User truy cập protected route (vd: /dashboard)
2. ProtectedRoute component kiểm tra isAuthenticated
3. Nếu false → redirect đến /login
4. Nếu true → render component bình thường

6. DEMO ACCOUNTS VÀ TESTING
================================================================================

👥 TÀI KHOẢN TEST:

Admin:   username: admin  | password: admin123  | role: admin
Doctor:  username: doctor | password: doctor123 | role: doctor  
Nurse:   username: nurse  | password: nurse123  | role: nurse

🧪 CÁCH TEST:

1. Mở http://localhost:3000/login
2. Nhập một trong các tài khoản demo
3. Verify redirect đến /chatbot sau khi login thành công
4. Check localStorage có user data
5. Test logout button → verify redirect về /login
6. Test truy cập /dashboard khi chưa login → redirect /login

7. KẾT LUẬN
================================================================================

✅ ĐIỂM MẠNH:
- Kiến trúc rõ ràng, tách biệt frontend/backend
- JWT authentication an toàn
- Route protection hiệu quả
- Session persistence với localStorage
- Error handling tốt

⚠️ LƯU Ý:
- Đây là mock database, production cần real database
- SECRET_KEY cần thay đổi trong production
- Cần implement refresh token cho security tốt hơn

Hệ thống hoạt động ổn định và phù hợp cho development/demo.

================================================================================
