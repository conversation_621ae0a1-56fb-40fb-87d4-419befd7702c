import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../UI/LoadingSpinner';

const PatientDetailsModal = ({ isOpen, onClose, patientId }) => {
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [isAnimating, setIsAnimating] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (isOpen && patientId) {
      setIsAnimating(true);
      setShowContent(false);
      fetchPatientDetails();
    } else if (!isOpen) {
      setIsAnimating(false);
      setShowContent(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, patientId]);

  const fetchPatientDetails = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch(`/api/v0/patients/${patientId}/details`);
      const data = await response.json();
      
      if (response.ok) {
        setPatientData(data.data);
        // Smooth transition to show content
        setTimeout(() => setShowContent(true), 100);
      } else {
        setError(data.error || 'Failed to fetch patient details');
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return 'N/A';
    try {
      return new Date(dateTimeString).toLocaleString();
    } catch {
      return dateTimeString;
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black transition-opacity duration-300 flex items-center justify-center z-50 p-4 ${
      isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
    }`}>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full h-[75vh] flex flex-col overflow-hidden transform transition-all duration-300 ${
        isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            Patient Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex min-h-0">
          {loading ? (
            <div className="flex items-center justify-center flex-1">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center flex-1 p-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-md">
                <p className="text-red-700 dark:text-red-400">{error}</p>
                <button
                  onClick={fetchPatientDetails}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 underline"
                >
                  Try again
                </button>
              </div>
            </div>
          ) : patientData ? (
            <div className="flex flex-1 min-h-0">
              {/* Sidebar Navigation */}
              <div className="w-48 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-3 flex-shrink-0">
                <nav className="space-y-2">
                  {[
                    { id: 'overview', label: 'Overview', icon: 'fas fa-user' },
                    { id: 'consultations', label: 'Consultations', icon: 'fas fa-stethoscope' },
                    { id: 'biometrics', label: 'Vital Signs', icon: 'fas fa-heartbeat' },
                    { id: 'prescriptions', label: 'Medications', icon: 'fas fa-pills' },
                    { id: 'history', label: 'Medical History', icon: 'fas fa-history' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left px-3 py-2.5 rounded text-sm font-medium transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 shadow-sm'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200'
                      }`}
                    >
                      <i className={`${tab.icon} mr-3 text-base`}></i>
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Main Content */}
              <div className={`flex-1 overflow-y-auto p-4 min-h-0 transition-opacity duration-300 ${
                showContent && !loading ? 'opacity-100' : 'opacity-0'
              }`}>
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Personal Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{patientData.patient_info.full_name || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Patient ID</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{patientData.patient_info.patient_id || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Date of Birth</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{formatDate(patientData.patient_info.date_of_birth)}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Gender</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">
                            {patientData.patient_info.gender === 'M' ? 'Male' : patientData.patient_info.gender === 'F' ? 'Female' : 'N/A'}
                          </p>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{patientData.patient_info.phone || 'N/A'}</p>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Address</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{patientData.patient_info.address || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'consultations' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Consultations</h3>
                    {patientData.consultations.length > 0 ? (
                      <div className="space-y-4">
                        {patientData.consultations.map((consultation, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                {consultation.diagnosis || 'No diagnosis'}
                              </h4>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDateTime(consultation.date)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Doctor: {consultation.doctor_name || 'Unknown'}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Specialty: {consultation.specialty || 'N/A'}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No consultations found.</p>
                    )}
                  </div>
                )}

                {activeTab === 'biometrics' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Vital Signs</h3>
                    {patientData.biometrics.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                          <thead className="bg-gray-50 dark:bg-gray-900">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Date</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Temperature</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Blood Pressure</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Pulse</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">SpO2</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Resp. Rate</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {patientData.biometrics.map((reading, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {formatDateTime(reading.date)}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.temperature ? `${reading.temperature}°C` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.systolic_bp && reading.diastolic_bp 
                                    ? `${reading.systolic_bp}/${reading.diastolic_bp}` 
                                    : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.pulse ? `${reading.pulse} bpm` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.spo2 ? `${reading.spo2}%` : 'N/A'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {reading.respiratory_rate ? `${reading.respiratory_rate}/min` : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No vital signs recorded.</p>
                    )}
                  </div>
                )}

                {activeTab === 'prescriptions' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Medications</h3>
                    {patientData.prescriptions.length > 0 ? (
                      <div className="space-y-4">
                        {patientData.prescriptions.map((prescription, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                {prescription.medication_name}
                              </h4>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDate(prescription.date)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Form: {prescription.form || 'N/A'}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Dosage: {prescription.dosage || 'N/A'}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Prescribed for: {prescription.prescribed_for || 'N/A'}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No prescriptions found.</p>
                    )}
                  </div>
                )}

                {activeTab === 'history' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Medical History</h3>
                    {patientData.antecedents.length > 0 ? (
                      <div className="space-y-4">
                        {patientData.antecedents.map((antecedent, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                              {antecedent.condition}
                            </h4>
                            {antecedent.details && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {antecedent.details}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">No medical history recorded.</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsModal;
