import traceback
import time
import io
import pandas as pd
import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from app.utils.dependencies import get_vanna, get_cache
from config import Config

router = APIRouter()
config = Config()
logger = logging.getLogger(__name__)

@router.get("/generate_summary_stream")
async def generate_summary_stream_api(
    id: str,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Generate a streaming summary of the data results"""
    try:
        # Retrieve data from cache
        df = cache.get(id=id, field='df')
        question = cache.get(id=id, field='question')

        if df is None or question is None:
            raise HTTPException(status_code=404, detail={"type": "error", "error": f"Data not found in cache for id {id}"})

        # Retrieve current chat history for the Vanna instance
        vn.chat_history = cache.get(id=id, field='chat_history') or []

        if df.empty:
            # For empty dataframes, return a simple non-streaming response
            def empty_response():
                import json
                yield f"data: {json.dumps({'type': 'summary_result', 'id': id, 'summary': 'No data available to summarize.', 'stream_end': True})}\n\n"

            return StreamingResponse(empty_response(), media_type='text/event-stream')
        else:
            # Use the streaming summary method
            response_stream = vn.generate_summary_stream(question=question, df=df, current_id=id)
            return StreamingResponse(response_stream, media_type='text/event-stream')

    except Exception as e:
        traceback.print_exc()
        def error_response():
            import json
            # Ensure id is available for error response
            error_id = id if 'id' in locals() else 'unknown'
            yield f"data: {json.dumps({'type': 'error', 'id': error_id, 'error': str(e), 'stream_end': True})}\n\n"

        return StreamingResponse(error_response(), media_type='text/event-stream')

@router.get("/generate_plotly_figure")
async def generate_plotly_figure_api(
    id: str,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Generate a Plotly chart for the data"""
    try:
        # Retrieve data from cache
        df = cache.get(id=id, field='df')
        question = cache.get(id=id, field='question')
        sql = cache.get(id=id, field='sql')

        if df is None or question is None or sql is None:
             raise HTTPException(status_code=404, detail={"type": "error", "error": f"Required data not found in cache for id {id}"})

        # Retrieve current chat history for the Vanna instance
        vn.chat_history = cache.get(id=id, field='chat_history') or []

        if df.empty:
            return {
                "type": "error",
                "id": id,
                "error": "No data available to generate chart"
            }

        # Use Vanna's chart generation logic
        should_generate, plotly_code = vn.should_generate_chart_with_context(
            question=question,
            sql=sql,
            df=df
        )

        if not should_generate or not plotly_code:
            return {
                "type": "no_chart",
                "id": id,
                "message": "Chart generation not recommended for this data"
            }

        # Execute the plotly code to generate the figure
        try:
            # Create a safe execution environment
            exec_globals = {
                'df': df,
                'pd': pd,
                'px': __import__('plotly.express', fromlist=['']),
                'go': __import__('plotly.graph_objects', fromlist=[''])
            }

            # Execute the plotly code
            exec(plotly_code, exec_globals)

            # Get the figure object
            fig = exec_globals.get('fig')
            if fig is None:
                raise ValueError("Plotly code did not create a 'fig' object")

            # Convert figure to JSON
            fig_json = fig.to_json()

            # Cache the chart data
            cache.set(id=id, field='chart', value=fig_json)
            cache.set(id=id, field='chart_code', value=plotly_code)
            cache.set(id=id, field='chat_history', value=vn.chat_history.copy())

            return {
                "type": "plotly_figure",
                "id": id,
                "fig": fig_json,
                "plotly_code": plotly_code
            }

        except Exception as e:
            logger.error(f"Error executing plotly code: {str(e)}")
            return {
                "type": "error",
                "id": id,
                "error": f"Failed to generate chart: {str(e)}",
                "plotly_code": plotly_code
            }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"type": "error", "error": str(e)})

